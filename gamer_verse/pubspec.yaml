name: gamer_verse
description: A futuristic social media platform exclusively for gamers
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # Firebase Core Services
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8
  firebase_performance: ^0.9.3+8
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # UI & Design
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_grid_view: ^0.7.0
  photo_view: ^0.14.0
  carousel_slider: ^4.2.1
  
  # Navigation
  go_router: ^12.1.3
  
  # Authentication
  google_sign_in: ^6.1.6
  sign_in_with_apple: ^5.0.0
  
  # Media & Video
  video_player: ^2.8.1
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  photo_manager: ^2.8.1
  video_compress: ^3.1.2
  chewie: ^1.7.4
  video_thumbnail: ^0.5.3
  
  # Audio & Voice
  record: ^5.0.4
  audioplayers: ^5.2.1
  permission_handler: ^11.1.0
  just_audio: ^0.9.36
  
  # Real-time Communication
  agora_rtc_engine: ^6.2.6
  web_socket_channel: ^2.4.0
  socket_io_client: ^2.0.3+1
  
  # Networking & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  connectivity_plus: ^5.0.2
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.0
  
  # Utils
  intl: ^0.18.1
  uuid: ^4.2.1
  url_launcher: ^6.2.2
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.1
  path_provider: ^2.1.1
  file_picker: ^6.1.1
  
  # Notifications
  flutter_local_notifications: ^16.3.0
  flutter_app_badger: ^1.5.0
  
  # GIF & Emoji
  giphy_picker: ^1.0.0
  emoji_picker_flutter: ^1.6.3
  
  # QR Code & Barcodes
  qr_flutter: ^4.1.0
  mobile_scanner: ^3.5.6
  
  # Charts & Analytics
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^24.1.47
  
  # AI & ML
  tflite_flutter: ^0.10.4
  image: ^4.1.3
  
  # AR & VR (Future Features)
  ar_flutter_plugin: ^0.7.3
  model_viewer_plus: ^1.7.0
  
  # Gaming APIs
  steam_api: ^1.0.0
  riot_api: ^1.0.0
  
  # Social Features
  share_plus: ^7.2.1
  flutter_share_me: ^1.2.0
  
  # Payment & Monetization
  stripe_platform_interface: ^8.0.0
  in_app_purchase: ^3.1.13
  
  # Advanced UI
  flutter_animate: ^4.3.0
  rive: ^0.12.4
  flutter_svg: ^2.0.9
  flutter_staggered_animations: ^1.1.1
  
  # Background Processing
  workmanager: ^0.5.2
  background_fetch: ^1.3.2
  
  # Security
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3
  
  # Testing
  mockito: ^5.4.4
  build_runner: ^2.4.7

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  riverpod_generator: ^2.3.9
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^7.0.8
  hive_generator: ^2.0.1
  mockito: ^5.4.4

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/audio/
    - assets/fonts/
  
  fonts:
    - family: Gaming
      fonts:
        - asset: assets/fonts/Gaming-Regular.ttf
        - asset: assets/fonts/Gaming-Bold.ttf
          weight: 700
    - family: Futura
      fonts:
        - asset: assets/fonts/Futura-Regular.ttf
        - asset: assets/fonts/Futura-Bold.ttf
          weight: 700
