# 🎮 GamerVerse Role-Based System

A clean, modular Flutter project structure with separate folders for each user role (User, Admin, Super Admin). This system provides role-based access control and dedicated interfaces for different user types in the GamerVerse gaming social media platform.

## 🏗️ Project Structure

```
lib/
└── roles/
    ├── user/
    │    ├── dashboard.dart          # 👤 User Dashboard
    │    ├── widgets/                # User-specific widgets
    │    └── services/
    │         └── user_service.dart  # User operations
    │
    ├── admin/
    │    ├── dashboard.dart          # 🧑‍💻 Admin Dashboard
    │    ├── widgets/                # Admin-specific widgets
    │    └── services/
    │         └── admin_service.dart # Admin operations
    │
    ├── super_admin/
    │    ├── dashboard.dart          # 👑 Super Admin Dashboard
    │    ├── widgets/                # Super admin widgets
    │    └── services/
    │         └── super_admin_service.dart # Platform management
    │
    └── role_router.dart             # 🚦 Role-based navigation
```

## 🎯 Overview

### 👤 **User Role** (Regular Users)
- **Social Features**: Feed, reels, messaging, profile management
- **Gaming Features**: Game integration, tournaments, squad finding
- **Content Creation**: Posts, videos, live streaming
- **Community**: Following, likes, comments, notifications

### 🧑‍💻 **Admin Role** (Community Guardians)
- **User Management**: Ban, suspend, verify users
- **Content Moderation**: Approve/remove posts, AI-powered analysis
- **Tournament Oversight**: Approve community events
- **Report Handling**: Process abuse reports and violations
- **Analytics**: Monitor platform activity and engagement

### 👑 **Super Admin Role** (Platform Owners)
- **Admin Management**: Create, edit, remove admin roles
- **Monetization Control**: Subscriptions, pricing, revenue analytics
- **Platform Settings**: XP system, badges, gamification rules
- **Security & Compliance**: GDPR, fraud detection, audit trails
- **Business Intelligence**: Advanced analytics and insights

## 🔐 Role-Based Access Control (RBAC)

### Role Hierarchy
```dart
enum UserRole {
  user,        // Regular users - basic access
  admin,       // Community moderators - management access
  superAdmin,  // Platform owners - full access
}
```

### Permission System
```dart
// Role hierarchy: user < admin < superAdmin
static bool hasPermission(UserRole currentRole, UserRole requiredRole) {
  switch (requiredRole) {
    case UserRole.user:
      return true; // All roles can access user features
    case UserRole.admin:
      return currentRole == UserRole.admin || currentRole == UserRole.superAdmin;
    case UserRole.superAdmin:
      return currentRole == UserRole.superAdmin;
  }
}
```

## 🚦 Role Router System

### Core Components

#### **RoleRouter Widget**
- Fetches user role from backend (Firebase/API)
- Shows loading screen during role detection
- Routes to appropriate dashboard based on role
- Handles role changes and updates
- Provides error handling and fallbacks

#### **RoleService Class**
```dart
class RoleService {
  // Fetch user role from backend
  static Future<UserRole> getUserRole() async {
    // TODO: Implement actual role fetching logic
    // Example: Check Firebase Auth and Firestore
    return UserRole.user;
  }

  // Check permissions
  static bool hasPermission(UserRole currentRole, UserRole requiredRole) {
    // Role hierarchy implementation
  }

  // Get role display information
  static String getRoleDisplayName(UserRole role) { ... }
  static IconData getRoleIcon(UserRole role) { ... }
}
```

#### **RoleSwitcher Widget** (Development Only)
- Allows developers to switch between roles for testing
- Shows current role with checkmark
- Easy role switching during development
- Remove in production or add proper authentication

### Usage Example
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  runApp(
    ProviderScope(
      child: MaterialApp(
        home: RoleRouter(), // Automatically routes based on user role
      ),
    ),
  );
}
```

## 📱 Dashboard Features

### 👤 User Dashboard
```dart
class UserDashboard extends ConsumerStatefulWidget {
  // Features:
  // - Social feed with gaming content
  // - Quick actions (create post, join tournament, find squad)
  // - Gaming feed with posts from other users
  // - Tabbed interface (Feed, Reels, Explore, Messages, Profile)
  // - Role switcher for development
}
```

**Key Features:**
- **Welcome Section**: Personalized greeting with user info
- **Quick Actions**: Create post, join tournament, find squad, live stream
- **Gaming Feed**: Social posts with gaming content
- **Tabbed Navigation**: Feed, Reels, Explore, Messages, Profile
- **Floating Action Button**: Context-aware actions per tab

### 🧑‍💻 Admin Dashboard
```dart
class AdminDashboard extends ConsumerStatefulWidget {
  // Features:
  // - Platform overview with key metrics
  // - Pending actions (reports, content, tournaments)
  // - Recent activity feed
  // - Tabbed interface (Dashboard, Users, Content, Tournaments, Analytics)
}
```

**Key Features:**
- **Admin Welcome**: Community guardian greeting
- **Platform Overview**: Active users, reports, content pending, tournaments
- **Pending Actions**: User reports, content review, tournament approval, verification requests
- **Recent Activity**: Admin actions with timestamps and details
- **Tabbed Interface**: Dashboard, Users, Content, Tournaments, Analytics

### 👑 Super Admin Dashboard
```dart
class SuperAdminDashboard extends ConsumerStatefulWidget {
  // Features:
  // - Platform statistics and revenue overview
  // - System health monitoring
  // - Critical alerts and security status
  // - Tabbed interface (Overview, Admins, Monetization, Platform, Security, Analytics)
}
```

**Key Features:**
- **Platform Overview**: Total users, revenue, active admins, security score
- **Revenue Overview**: Subscription, in-app purchases, advertising, sponsorships
- **System Health**: Server status, database, CDN, API performance
- **Critical Alerts**: High CPU usage, database issues, security threats
- **Tabbed Interface**: Overview, Admins, Monetization, Platform, Security, Analytics

## 🔧 Service Layer

### User Service
```dart
class UserService {
  // User profile operations
  static Future<Map<String, dynamic>> getUserProfile(String userId) async { ... }
  
  // Content creation
  static Future<bool> createPost({...}) async { ... }
  static Future<bool> likePost(String postId, String userId) async { ... }
  static Future<bool> commentOnPost({...}) async { ... }
  
  // Social interactions
  static Future<bool> followUser(String targetUserId, String currentUserId) async { ... }
  static Future<bool> sendMessage({...}) async { ... }
  
  // Gaming features
  static Future<Map<String, dynamic>> getGamingStats(String userId) async { ... }
  static Future<List<Map<String, dynamic>>> getUserFeed(String userId) async { ... }
  
  // Notifications
  static Future<List<Map<String, dynamic>>> getNotifications(String userId) async { ... }
}
```

### Admin Service
```dart
class AdminService {
  // Report management
  static Future<List<Map<String, dynamic>>> getPendingReports() async { ... }
  
  // User management
  static Future<bool> banUser({...}) async { ... }
  static Future<bool> suspendUser({...}) async { ... }
  static Future<bool> verifyUser({...}) async { ... }
  
  // Content moderation
  static Future<bool> moderateContent({...}) async { ... }
  static Future<List<Map<String, dynamic>>> getPendingContent() async { ... }
  
  // Tournament management
  static Future<List<Map<String, dynamic>>> getPendingTournaments() async { ... }
  static Future<bool> approveTournament({...}) async { ... }
  
  // Analytics and monitoring
  static Future<Map<String, dynamic>> getAdminAnalytics() async { ... }
  static Future<List<Map<String, dynamic>>> getRecentActivity() async { ... }
  
  // AI-powered analysis
  static Future<Map<String, dynamic>> analyzeContent(String contentId) async { ... }
  static Future<Map<String, dynamic>> analyzeUserBehavior(String userId) async { ... }
}
```

### Super Admin Service
```dart
class SuperAdminService {
  // Platform overview
  static Future<Map<String, dynamic>> getPlatformOverview() async { ... }
  static Future<Map<String, dynamic>> getFinancialAnalytics() async { ... }
  
  // Admin management
  static Future<List<Map<String, dynamic>>> getAdminList() async { ... }
  static Future<bool> createAdmin({...}) async { ... }
  static Future<bool> updateAdminPermissions({...}) async { ... }
  
  // Platform settings
  static Future<Map<String, dynamic>> getPlatformSettings() async { ... }
  static Future<bool> updatePlatformSettings({...}) async { ... }
  
  // Security and compliance
  static Future<Map<String, dynamic>> getSecurityCompliance() async { ... }
  static Future<Map<String, dynamic>> getSystemHealth() async { ... }
  
  // Advanced analytics
  static Future<Map<String, dynamic>> getAdvancedAnalytics() async { ... }
  static Future<Map<String, dynamic>> generateBusinessReport({...}) async { ... }
}
```

## 🚀 Getting Started

### 1. Setup Firebase Integration
```dart
// In role_router.dart, implement actual role fetching:
static Future<UserRole> getUserRole() async {
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return UserRole.user;
  
  final userDoc = await FirebaseFirestore.instance
      .collection('users')
      .doc(user.uid)
      .get();
  
  final role = userDoc.data()?['role'] ?? 'user';
  return UserRole.values.firstWhere(
    (e) => e.toString().split('.').last == role,
    orElse: () => UserRole.user,
  );
}
```

### 2. Configure Security Rules
```javascript
// Firestore security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // User data
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == userId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin']);
    }
    
    // Admin-only collections
    match /admin_actions/{docId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin'];
    }
    
    // Super admin-only collections
    match /platform_settings/{docId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'super_admin';
    }
  }
}
```

### 3. Initialize the App
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  runApp(
    ProviderScope(
      child: MaterialApp(
        title: 'GamerVerse',
        theme: AppTheme.darkTheme,
        home: RoleRouter(), // Automatically routes based on user role
      ),
    ),
  );
}
```

## 🔮 Future Enhancements

### Role Extensions
- **Moderator Role**: Content-specific moderation
- **Esports Admin**: Tournament and competition management
- **Support Agent**: User support and ticket handling
- **Analyst Role**: Data analysis and reporting

### Advanced Features
- **Real-time Role Updates**: Live role changes without app restart
- **Role-based Feature Flags**: Dynamic feature enabling/disabling
- **Custom Permissions**: Granular permission system
- **Role Inheritance**: Hierarchical role system
- **Temporary Roles**: Time-limited role assignments

### Security Enhancements
- **Two-Factor Authentication**: Enhanced security for admin roles
- **Session Management**: Role-based session timeouts
- **Audit Logging**: Complete action tracking
- **IP Restrictions**: Geographic and IP-based access control

### Integration Features
- **External APIs**: Game platform integrations
- **Analytics Integration**: Business intelligence tools
- **Notification System**: Role-based notifications
- **Backup Systems**: Data protection and recovery

## 📋 Development Guidelines

### Code Organization
- Keep role-specific code in respective folders
- Use consistent naming conventions
- Implement proper error handling
- Add comprehensive documentation

### Testing
- Test role switching functionality
- Verify permission checks
- Test error scenarios
- Validate UI for each role

### Performance
- Implement lazy loading for role-specific features
- Cache role information appropriately
- Optimize service calls
- Monitor memory usage

### Security
- Validate all role-based operations
- Implement proper authentication
- Use secure communication
- Regular security audits

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

### Development Workflow
- Use the RoleSwitcher for testing different roles
- Implement services with proper error handling
- Add comprehensive comments for future developers
- Follow the existing code structure and patterns

---

**GamerVerse Role-Based System** - Empowering Different User Types 🎮

*Modular. Scalable. Secure.*
