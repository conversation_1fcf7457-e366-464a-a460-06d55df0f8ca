import 'package:flutter/material.dart';

class AppColors {
  // Primary Futuristic Colors
  static const Color primary = Color(0xFF00D4FF); // Cyber Blue
  static const Color primaryDark = Color(0xFF0099CC);
  static const Color primaryLight = Color(0xFF66E6FF);
  
  // Secondary Colors
  static const Color secondary = Color(0xFFFF0080); // Neon Pink
  static const Color secondaryDark = Color(0xFFCC0066);
  static const Color secondaryLight = Color(0xFFFF66B3);
  
  // Accent Colors
  static const Color accent = Color(0xFF00FF41); // Matrix Green
  static const Color accentDark = Color(0xFF00CC33);
  static const Color accentLight = Color(0xFF66FF80);
  
  // Gaming Colors
  static const Color neonPurple = Color(0xFF8A2BE2);
  static const Color neonOrange = Color(0xFFFF6B35);
  static const Color neonYellow = Color(0xFFFFD700);
  static const Color neonRed = Color(0xFFFF1744);
  static const Color neonCyan = Color(0xFF00BCD4);
  
  // Background Colors (Cyberpunk Dark)
  static const Color background = Color(0xFF0A0A0F);
  static const Color surface = Color(0xFF1A1A2E);
  static const Color card = Color(0xFF16213E);
  static const Color cardLight = Color(0xFF242B42);
  static const Color cardDark = Color(0xFF0F1419);
  
  // Text Colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB0B0B0);
  static const Color textTertiary = Color(0xFF808080);
  static const Color textAccent = Color(0xFF00D4FF);
  
  // Status Colors
  static const Color success = Color(0xFF00FF41);
  static const Color warning = Color(0xFFFFD700);
  static const Color error = Color(0xFFFF1744);
  static const Color info = Color(0xFF00BCD4);
  
  // Gaming Status Colors
  static const Color online = Color(0xFF00FF41);
  static const Color offline = Color(0xFF808080);
  static const Color inGame = Color(0xFFFF6B35);
  static const Color streaming = Color(0xFFFF0080);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, secondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient cyberGradient = LinearGradient(
    colors: [neonCyan, neonPurple, neonPink],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient gamingGradient = LinearGradient(
    colors: [background, surface, card],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient neonGradient = LinearGradient(
    colors: [neonGreen, neonBlue, neonPink, neonPurple],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Glass Effect Colors
  static const Color glassBackground = Color(0x1AFFFFFF);
  static const Color glassBorder = Color(0x33FFFFFF);
  
  // Shadow Colors
  static const Color neonShadow = Color(0x4000D4FF);
  static const Color cyberShadow = Color(0x40FF0080);
  
  // Platform Colors
  static const Color steamColor = Color(0xFF171a21);
  static const Color psnColor = Color(0xFF006FCD);
  static const Color xboxColor = Color(0xFF107C10);
  static const Color mobileColor = Color(0xFF00BCD4);
  
  // Achievement Colors
  static const Color bronze = Color(0xFFCD7F32);
  static const Color silver = Color(0xFFC0C0C0);
  static const Color gold = Color(0xFFFFD700);
  static const Color platinum = Color(0xFFE5E4E2);
  
  // XP Level Colors
  static const Color level1 = Color(0xFF808080);
  static const Color level10 = Color(0xFF00FF41);
  static const Color level25 = Color(0xFF00BCD4);
  static const Color level50 = Color(0xFF8A2BE2);
  static const Color level100 = Color(0xFFFFD700);
}
