import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';

class FirebaseService {
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
  static FirebaseMessaging get messaging => FirebaseMessaging.instance;
  static FirebaseAnalytics get analytics => FirebaseAnalytics.instance;
  static FirebaseCrashlytics get crashlytics => FirebaseCrashlytics.instance;
  static FirebasePerformance get performance => FirebasePerformance.instance;

  static Future<void> initialize() async {
    try {
      // Initialize Firebase
      await Firebase.initializeApp();
      
      // Request notification permissions
      await _requestNotificationPermissions();
      
      // Configure Firestore settings
      await _configureFirestore();
      
      // Configure Crashlytics
      await _configureCrashlytics();
      
      // Configure Performance Monitoring
      await _configurePerformance();
      
      print('🔥 Firebase services initialized successfully');
    } catch (e) {
      print('❌ Error initializing Firebase services: $e');
      rethrow;
    }
  }

  static Future<void> _requestNotificationPermissions() async {
    try {
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      
      print('📱 User granted permission: ${settings.authorizationStatus}');
    } catch (e) {
      print('❌ Error requesting notification permissions: $e');
    }
  }

  static Future<void> _configureFirestore() async {
    try {
      // Enable offline persistence
      await firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );
    } catch (e) {
      print('❌ Error configuring Firestore: $e');
    }
  }

  static Future<void> _configureCrashlytics() async {
    try {
      // Enable Crashlytics collection
      await crashlytics.setCrashlyticsCollectionEnabled(true);
      
      // Set user identifier when user logs in
      final user = auth.currentUser;
      if (user != null) {
        await crashlytics.setUserIdentifier(user.uid);
      }
    } catch (e) {
      print('❌ Error configuring Crashlytics: $e');
    }
  }

  static Future<void> _configurePerformance() async {
    try {
      // Enable Performance Monitoring
      await performance.setPerformanceCollectionEnabled(true);
    } catch (e) {
      print('❌ Error configuring Performance: $e');
    }
  }

  // Get current user
  static User? get currentUser => auth.currentUser;

  // Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;

  // Get user ID
  static String? get userId => currentUser?.uid;

  // Sign out
  static Future<void> signOut() async {
    try {
      await auth.signOut();
    } catch (e) {
      print('❌ Error signing out: $e');
      rethrow;
    }
  }

  // Delete user account
  static Future<void> deleteAccount() async {
    try {
      await currentUser?.delete();
    } catch (e) {
      print('❌ Error deleting account: $e');
      rethrow;
    }
  }

  // Get FCM token
  static Future<String?> getFCMToken() async {
    try {
      return await messaging.getToken();
    } catch (e) {
      print('❌ Error getting FCM token: $e');
      return null;
    }
  }

  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await messaging.subscribeToTopic(topic);
    } catch (e) {
      print('❌ Error subscribing to topic: $e');
    }
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await messaging.unsubscribeFromTopic(topic);
    } catch (e) {
      print('❌ Error unsubscribing from topic: $e');
    }
  }

  // Log analytics event
  static Future<void> logEvent(String name, Map<String, dynamic>? parameters) async {
    try {
      await analytics.logEvent(name: name, parameters: parameters);
    } catch (e) {
      print('❌ Error logging analytics event: $e');
    }
  }

  // Set user properties
  static Future<void> setUserProperties(Map<String, String> properties) async {
    try {
      for (var entry in properties.entries) {
        await analytics.setUserProperty(name: entry.key, value: entry.value);
      }
    } catch (e) {
      print('❌ Error setting user properties: $e');
    }
  }

  // Record custom trace
  static Future<void> recordTrace(String name, Future<void> Function() operation) async {
    try {
      final trace = performance.newTrace(name);
      await trace.start();
      await operation();
      await trace.stop();
    } catch (e) {
      print('❌ Error recording trace: $e');
    }
  }

  // Upload file to Firebase Storage
  static Future<String> uploadFile(String path, List<int> bytes) async {
    try {
      final ref = storage.ref().child(path);
      final uploadTask = ref.putData(bytes);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      print('❌ Error uploading file: $e');
      rethrow;
    }
  }

  // Delete file from Firebase Storage
  static Future<void> deleteFile(String path) async {
    try {
      final ref = storage.ref().child(path);
      await ref.delete();
    } catch (e) {
      print('❌ Error deleting file: $e');
      rethrow;
    }
  }
}
