import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:gamer_verse/shared/services/firebase_service.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  static Future<void> initialize() async {
    try {
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();
      
      // Initialize app badge
      await _initializeAppBadge();
      
      print('🔔 Notification service initialized successfully');
    } catch (e) {
      print('❌ Error initializing notification service: $e');
    }
  }

  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    
    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  static Future<void> _initializeFirebaseMessaging() async {
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle notification taps when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  static Future<void> _initializeAppBadge() async {
    try {
      bool isSupported = await FlutterAppBadger.isAppBadgeSupported();
      if (isSupported) {
        FlutterAppBadger.updateBadgeCount(0);
      }
    } catch (e) {
      print('❌ Error initializing app badge: $e');
    }
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // Handle local notification tap
    print('📱 Local notification tapped: ${response.payload}');
    // TODO: Navigate to specific screen based on payload
  }

  static void _handleForegroundMessage(RemoteMessage message) {
    print('📱 Foreground message received: ${message.notification?.title}');
    
    // Show local notification
    _showLocalNotification(
      title: message.notification?.title ?? 'GamerVerse',
      body: message.notification?.body ?? '',
      payload: message.data.toString(),
    );
  }

  static void _handleNotificationTap(RemoteMessage message) {
    print('📱 Background notification tapped: ${message.notification?.title}');
    // TODO: Navigate to specific screen based on message data
  }

  // Show local notification
  static Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'gamer_verse_channel',
      'GamerVerse Notifications',
      channelDescription: 'Notifications for GamerVerse app',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
    );
    
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );
    
    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  // Show gaming-specific notifications
  static Future<void> showGamingNotification({
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    // Log analytics event
    await FirebaseService.logEvent('notification_shown', {
      'type': type,
      'title': title,
    });
    
    await _showLocalNotification(
      title: title,
      body: body,
      payload: data?.toString(),
    );
  }

  // Show like notification
  static Future<void> showLikeNotification(String username) async {
    await showGamingNotification(
      title: '🔥 New Like!',
      body: '$username liked your post',
      type: 'like',
    );
  }

  // Show comment notification
  static Future<void> showCommentNotification(String username, String comment) async {
    await showGamingNotification(
      title: '💬 New Comment',
      body: '$username: ${comment.length > 50 ? '${comment.substring(0, 50)}...' : comment}',
      type: 'comment',
    );
  }

  // Show follow notification
  static Future<void> showFollowNotification(String username) async {
    await showGamingNotification(
      title: '👥 New Follower',
      body: '$username started following you',
      type: 'follow',
    );
  }

  // Show squad invite notification
  static Future<void> showSquadInviteNotification(String username, String game) async {
    await showGamingNotification(
      title: '🎮 Squad Invite',
      body: '$username invited you to play $game',
      type: 'squad_invite',
    );
  }

  // Show tournament notification
  static Future<void> showTournamentNotification(String tournamentName) async {
    await showGamingNotification(
      title: '🏆 Tournament Alert',
      body: 'New tournament: $tournamentName',
      type: 'tournament',
    );
  }

  // Show achievement notification
  static Future<void> showAchievementNotification(String achievementName) async {
    await showGamingNotification(
      title: '🏅 Achievement Unlocked!',
      body: 'You earned: $achievementName',
      type: 'achievement',
    );
  }

  // Update app badge count
  static Future<void> updateBadgeCount(int count) async {
    try {
      await FlutterAppBadger.updateBadgeCount(count);
    } catch (e) {
      print('❌ Error updating badge count: $e');
    }
  }

  // Clear app badge
  static Future<void> clearBadge() async {
    try {
      FlutterAppBadger.removeBadge();
    } catch (e) {
      print('❌ Error clearing badge: $e');
    }
  }

  // Subscribe to gaming topics
  static Future<void> subscribeToGamingTopics(List<String> games) async {
    for (String game in games) {
      await FirebaseService.subscribeToTopic('game_$game');
    }
  }

  // Unsubscribe from gaming topics
  static Future<void> unsubscribeFromGamingTopics(List<String> games) async {
    for (String game in games) {
      await FirebaseService.unsubscribeFromTopic('game_$game');
    }
  }
}

// Background message handler
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('📱 Background message received: ${message.notification?.title}');
  // Handle background messages here
}
