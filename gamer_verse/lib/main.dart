import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:gamer_verse/core/constants/app_colors.dart';
import 'package:gamer_verse/core/constants/app_theme.dart';
import 'package:gamer_verse/features/auth/presentation/providers/auth_provider.dart';
import 'package:gamer_verse/features/auth/presentation/screens/splash_screen.dart';
import 'package:gamer_verse/shared/services/firebase_service.dart';
import 'package:gamer_verse/shared/services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Initialize Firebase services
  await FirebaseService.initialize();
  
  // Initialize notification service
  await NotificationService.initialize();
  
  runApp(
    const ProviderScope(
      child: GamerVerseApp(),
    ),
  );
}

class GamerVerseApp extends ConsumerWidget {
  const GamerVerseApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'GamerVerse',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.dark, // Default to dark theme for gaming
      home: const SplashScreen(),
    );
  }
}
