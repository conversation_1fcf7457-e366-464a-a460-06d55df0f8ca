import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gamer_verse/roles/user/dashboard.dart';
import 'package:gamer_verse/roles/admin/dashboard.dart';
import 'package:gamer_verse/roles/super_admin/dashboard.dart';

/// Enum defining all possible user roles in the application
enum UserRole {
  user,
  admin,
  superAdmin,
}

/// Provider for managing the current user's role
/// This can be extended to fetch from Firebase Auth, API, or local storage
final userRoleProvider = StateProvider<UserRole?>((ref) => null);

/// Service class for handling role-related operations
class RoleService {
  /// Fetches the user's role from the backend (Firebase, API, etc.)
  /// 
  /// This method should be implemented to:
  /// 1. Check Firebase Auth for user authentication
  /// 2. Query Firestore/API for user role information
  /// 3. Handle role caching for performance
  /// 4. Manage role updates in real-time
  /// 
  /// Example implementation:
  /// ```dart
  /// static Future<UserRole> getUserRole() async {
  ///   final user = FirebaseAuth.instance.currentUser;
  ///   if (user == null) return UserRole.user;
  ///   
  ///   final userDoc = await FirebaseFirestore.instance
  ///       .collection('users')
  ///       .doc(user.uid)
  ///       .get();
  ///   
  ///   final role = userDoc.data()?['role'] ?? 'user';
  ///   return UserRole.values.firstWhere(
  ///     (e) => e.toString().split('.').last == role,
  ///     orElse: () => UserRole.user,
  ///   );
  /// }
  /// ```
  static Future<UserRole> getUserRole() async {
    // TODO: Implement actual role fetching logic
    // For now, return a default role
    // This should be replaced with actual Firebase/API integration
    
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Example: Check if user is authenticated and fetch role
    // final user = FirebaseAuth.instance.currentUser;
    // if (user == null) return UserRole.user;
    
    // For demonstration, return user role
    // In production, fetch from Firestore/API
    return UserRole.user;
  }

  /// Checks if the current user has the required role or higher
  /// 
  /// Role hierarchy: user < admin < superAdmin
  static bool hasPermission(UserRole currentRole, UserRole requiredRole) {
    switch (requiredRole) {
      case UserRole.user:
        return true; // All roles can access user features
      case UserRole.admin:
        return currentRole == UserRole.admin || currentRole == UserRole.superAdmin;
      case UserRole.superAdmin:
        return currentRole == UserRole.superAdmin;
    }
  }

  /// Gets the display name for a role
  static String getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.user:
        return 'User';
      case UserRole.admin:
        return 'Admin';
      case UserRole.superAdmin:
        return 'Super Admin';
    }
  }

  /// Gets the icon for a role
  static IconData getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.user:
        return Icons.person;
      case UserRole.admin:
        return Icons.admin_panel_settings;
      case UserRole.superAdmin:
        return Icons.admin_panel_settings_outlined;
    }
  }
}

/// Main role router widget that handles role-based navigation
/// 
/// This widget:
/// 1. Fetches the user's role on initialization
/// 2. Shows a loading screen while fetching
/// 3. Navigates to the appropriate dashboard based on role
/// 4. Handles role changes and updates
/// 
/// To extend this for future features:
/// - Add role-based feature flags
/// - Implement role change listeners
/// - Add role validation and security checks
/// - Support for custom role permissions
class RoleRouter extends ConsumerStatefulWidget {
  const RoleRouter({super.key});

  @override
  ConsumerState<RoleRouter> createState() => _RoleRouterState();
}

class _RoleRouterState extends ConsumerState<RoleRouter> {
  bool _isLoading = true;
  UserRole? _userRole;

  @override
  void initState() {
    super.initState();
    _initializeRole();
  }

  /// Initialize user role on app startup
  /// 
  /// This method:
  /// 1. Fetches the user's role from the backend
  /// 2. Updates the provider state
  /// 3. Handles any errors during role fetching
  /// 4. Sets loading state appropriately
  Future<void> _initializeRole() async {
    try {
      final role = await RoleService.getUserRole();
      
      if (mounted) {
        ref.read(userRoleProvider.notifier).state = role;
        setState(() {
          _userRole = role;
          _isLoading = false;
        });
      }
    } catch (error) {
      // Handle role fetching errors
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        // Show error dialog or fallback to user role
        _showErrorDialog(error.toString());
      }
    }
  }

  /// Shows an error dialog when role fetching fails
  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Role Loading Error'),
        content: Text('Failed to load user role: $error'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Fallback to user role
              ref.read(userRoleProvider.notifier).state = UserRole.user;
              setState(() {
                _userRole = UserRole.user;
              });
            },
            child: const Text('Continue as User'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Listen to role changes from provider
    final currentRole = ref.watch(userRoleProvider);

    // Show loading screen while fetching role
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    // Show role-based dashboard
    return _buildRoleDashboard(currentRole ?? _userRole ?? UserRole.user);
  }

  /// Builds the loading screen while fetching user role
  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo or loading animation
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.blue, Colors.purple],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.games,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Loading your experience...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the appropriate dashboard based on user role
  /// 
  /// This method routes users to their role-specific dashboard:
  /// - User → UserDashboard
  /// - Admin → AdminDashboard  
  /// - Super Admin → SuperAdminDashboard
  /// 
  /// To add new roles in the future:
  /// 1. Add the new role to UserRole enum
  /// 2. Add a case here for the new role
  /// 3. Create the corresponding dashboard widget
  /// 4. Update RoleService methods
  Widget _buildRoleDashboard(UserRole role) {
    switch (role) {
      case UserRole.user:
        return const UserDashboard();
      case UserRole.admin:
        return const AdminDashboard();
      case UserRole.superAdmin:
        return const SuperAdminDashboard();
    }
  }
}

/// Extension methods for easier role handling
extension UserRoleExtension on UserRole {
  /// Check if this role has permission for a required role
  bool hasPermission(UserRole requiredRole) {
    return RoleService.hasPermission(this, requiredRole);
  }

  /// Get the display name for this role
  String get displayName => RoleService.getRoleDisplayName(this);

  /// Get the icon for this role
  IconData get icon => RoleService.getRoleIcon(this);
}

/// Widget for testing role switching (development only)
/// 
/// This widget allows developers to switch between roles for testing
/// Remove this in production or add proper authentication
class RoleSwitcher extends ConsumerWidget {
  const RoleSwitcher({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentRole = ref.watch(userRoleProvider);

    return PopupMenuButton<UserRole>(
      onSelected: (role) {
        ref.read(userRoleProvider.notifier).state = role;
      },
      itemBuilder: (context) => UserRole.values.map((role) {
        return PopupMenuItem(
          value: role,
          child: Row(
            children: [
              Icon(role.icon),
              const SizedBox(width: 8),
              Text(role.displayName),
              if (currentRole == role) ...[
                const SizedBox(width: 8),
                const Icon(Icons.check, size: 16),
              ],
            ],
          ),
        );
      }).toList(),
      child: const Padding(
        padding: EdgeInsets.all(8.0),
        child: Icon(Icons.swap_horiz),
      ),
    );
  }
}
