import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gamer_verse/features/admin/domain/models/admin_models.dart';
import 'package:gamer_verse/shared/services/firebase_service.dart';

class AdminService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  // User Management Methods
  static Future<void> banUser({
    required String userId,
    required String adminId,
    required String reason,
    required BanType banType,
    DateTime? expiresAt,
    Map<String, dynamic>? evidence,
  }) async {
    try {
      final action = UserManagementAction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        targetUserId: userId,
        adminId: adminId,
        action: UserAction.ban,
        reason: reason,
        details: {
          'banType': banType.toString().split('.').last,
          'expiresAt': expiresAt?.toIso8601String(),
        },
        timestamp: DateTime.now(),
        expiresAt: expiresAt,
        evidence: evidence ?? {},
      );

      await _firestore
          .collection('admin_actions')
          .doc(action.id)
          .set(action.toFirestore());

      // Update user status
      await _firestore.collection('users').doc(userId).update({
        'isBanned': true,
        'banDetails': {
          'bannedAt': Timestamp.now(),
          'bannedBy': adminId,
          'reason': reason,
          'banType': banType.toString().split('.').last,
          'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt) : null,
        },
      });

      // Log audit trail
      await _logAuditTrail(
        adminId: adminId,
        action: 'user_banned',
        targetType: 'user',
        targetId: userId,
        details: {
          'reason': reason,
          'banType': banType.toString().split('.').last,
          'expiresAt': expiresAt?.toIso8601String(),
        },
      );
    } catch (e) {
      throw Exception('Failed to ban user: $e');
    }
  }

  static Future<void> suspendUser({
    required String userId,
    required String adminId,
    required String reason,
    required Duration duration,
    Map<String, dynamic>? evidence,
  }) async {
    try {
      final expiresAt = DateTime.now().add(duration);
      
      final action = UserManagementAction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        targetUserId: userId,
        adminId: adminId,
        action: UserAction.suspend,
        reason: reason,
        details: {
          'duration': duration.inSeconds,
          'expiresAt': expiresAt.toIso8601String(),
        },
        timestamp: DateTime.now(),
        expiresAt: expiresAt,
        evidence: evidence ?? {},
      );

      await _firestore
          .collection('admin_actions')
          .doc(action.id)
          .set(action.toFirestore());

      // Update user status
      await _firestore.collection('users').doc(userId).update({
        'isSuspended': true,
        'suspensionDetails': {
          'suspendedAt': Timestamp.now(),
          'suspendedBy': adminId,
          'reason': reason,
          'expiresAt': Timestamp.fromDate(expiresAt),
        },
      });

      await _logAuditTrail(
        adminId: adminId,
        action: 'user_suspended',
        targetType: 'user',
        targetId: userId,
        details: {
          'reason': reason,
          'duration': duration.inSeconds,
          'expiresAt': expiresAt.toIso8601String(),
        },
      );
    } catch (e) {
      throw Exception('Failed to suspend user: $e');
    }
  }

  static Future<void> verifyUser({
    required String userId,
    required String adminId,
    required String reason,
    Map<String, dynamic>? evidence,
  }) async {
    try {
      final action = UserManagementAction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        targetUserId: userId,
        adminId: adminId,
        action: UserAction.verify,
        reason: reason,
        timestamp: DateTime.now(),
        evidence: evidence ?? {},
      );

      await _firestore
          .collection('admin_actions')
          .doc(action.id)
          .set(action.toFirestore());

      // Update user verification status
      await _firestore.collection('users').doc(userId).update({
        'isVerified': true,
        'verifiedAt': Timestamp.now(),
        'verifiedBy': adminId,
        'verificationReason': reason,
      });

      await _logAuditTrail(
        adminId: adminId,
        action: 'user_verified',
        targetType: 'user',
        targetId: userId,
        details: {'reason': reason},
      );
    } catch (e) {
      throw Exception('Failed to verify user: $e');
    }
  }

  // Content Moderation Methods
  static Future<void> moderateContent({
    required String contentId,
    required String adminId,
    required ContentType contentType,
    required ContentAction action,
    required String reason,
    Map<String, dynamic>? details,
    double aiConfidence = 0.0,
  }) async {
    try {
      final moderationAction = ContentModerationAction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        contentId: contentId,
        adminId: adminId,
        contentType: contentType,
        action: action,
        reason: reason,
        details: details ?? {},
        timestamp: DateTime.now(),
        aiConfidence: aiConfidence,
      );

      await _firestore
          .collection('content_moderation')
          .doc(moderationAction.id)
          .set(moderationAction.toFirestore());

      // Update content status
      await _firestore.collection('content').doc(contentId).update({
        'moderationStatus': action.toString().split('.').last,
        'moderatedAt': Timestamp.now(),
        'moderatedBy': adminId,
        'moderationReason': reason,
      });

      await _logAuditTrail(
        adminId: adminId,
        action: 'content_moderated',
        targetType: 'content',
        targetId: contentId,
        details: {
          'contentType': contentType.toString().split('.').last,
          'action': action.toString().split('.').last,
          'reason': reason,
          'aiConfidence': aiConfidence,
        },
      );
    } catch (e) {
      throw Exception('Failed to moderate content: $e');
    }
  }

  // Report Management Methods
  static Future<List<UserReport>> getPendingReports() async {
    try {
      final snapshot = await _firestore
          .collection('reports')
          .where('status', isEqualTo: 'pending')
          .orderBy('timestamp', descending: true)
          .limit(50)
          .get();

      return snapshot.docs.map((doc) => UserReport.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get pending reports: $e');
    }
  }

  static Future<void> assignReport({
    required String reportId,
    required String adminId,
  }) async {
    try {
      await _firestore.collection('reports').doc(reportId).update({
        'assignedAdminId': adminId,
        'status': 'investigating',
        'assignedAt': Timestamp.now(),
      });

      await _logAuditTrail(
        adminId: adminId,
        action: 'report_assigned',
        targetType: 'report',
        targetId: reportId,
        details: {},
      );
    } catch (e) {
      throw Exception('Failed to assign report: $e');
    }
  }

  static Future<void> resolveReport({
    required String reportId,
    required String adminId,
    required String resolution,
    String? action,
  }) async {
    try {
      await _firestore.collection('reports').doc(reportId).update({
        'status': 'resolved',
        'resolvedAt': Timestamp.now(),
        'resolvedBy': adminId,
        'resolution': resolution,
        'action': action,
      });

      await _logAuditTrail(
        adminId: adminId,
        action: 'report_resolved',
        targetType: 'report',
        targetId: reportId,
        details: {
          'resolution': resolution,
          'action': action,
        },
      );
    } catch (e) {
      throw Exception('Failed to resolve report: $e');
    }
  }

  // Tournament Management Methods
  static Future<List<TournamentManagement>> getPendingTournaments() async {
    try {
      final snapshot = await _firestore
          .collection('tournament_management')
          .where('status', isEqualTo: TournamentStatus.pending.toString().split('.').last)
          .orderBy('timestamp', descending: true)
          .limit(20)
          .get();

      return snapshot.docs.map((doc) => TournamentManagement.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get pending tournaments: $e');
    }
  }

  static Future<void> approveTournament({
    required String tournamentId,
    required String adminId,
    required String reason,
    String? assignedModeratorId,
    Map<String, dynamic>? settings,
  }) async {
    try {
      final management = TournamentManagement(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        tournamentId: tournamentId,
        adminId: adminId,
        status: TournamentStatus.approved,
        reason: reason,
        timestamp: DateTime.now(),
        approvedAt: DateTime.now(),
        assignedModeratorId: assignedModeratorId,
        settings: settings ?? {},
      );

      await _firestore
          .collection('tournament_management')
          .doc(management.id)
          .set(management.toFirestore());

      // Update tournament status
      await _firestore.collection('tournaments').doc(tournamentId).update({
        'status': 'approved',
        'approvedAt': Timestamp.now(),
        'approvedBy': adminId,
        'assignedModeratorId': assignedModeratorId,
      });

      await _logAuditTrail(
        adminId: adminId,
        action: 'tournament_approved',
        targetType: 'tournament',
        targetId: tournamentId,
        details: {
          'reason': reason,
          'assignedModeratorId': assignedModeratorId,
        },
      );
    } catch (e) {
      throw Exception('Failed to approve tournament: $e');
    }
  }

  // Analytics Methods
  static Future<AdminAnalytics> getDailyAnalytics(DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final snapshot = await _firestore
          .collection('admin_analytics')
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('date', isLessThan: Timestamp.fromDate(endOfDay))
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return AdminAnalytics.fromFirestore(snapshot.docs.first);
      } else {
        // Return empty analytics if no data exists
        return AdminAnalytics(
          id: startOfDay.millisecondsSinceEpoch.toString(),
          date: startOfDay,
        );
      }
    } catch (e) {
      throw Exception('Failed to get daily analytics: $e');
    }
  }

  static Future<Map<String, dynamic>> getQuickStats() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // Get today's analytics
      final analytics = await getDailyAnalytics(today);

      // Get pending counts
      final pendingReports = await _firestore
          .collection('reports')
          .where('status', isEqualTo: 'pending')
          .count()
          .get();

      final pendingContent = await _firestore
          .collection('content')
          .where('moderationStatus', isEqualTo: 'pending')
          .count()
          .get();

      final pendingTournaments = await _firestore
          .collection('tournament_management')
          .where('status', isEqualTo: TournamentStatus.pending.toString().split('.').last)
          .count()
          .get();

      return {
        'activeUsers': analytics.activeUsers,
        'newUsers': analytics.newUsers,
        'bannedUsers': analytics.bannedUsers,
        'pendingReports': pendingReports.count,
        'pendingContent': pendingContent.count,
        'pendingTournaments': pendingTournaments.count,
        'averageResponseTime': analytics.averageResponseTime,
      };
    } catch (e) {
      throw Exception('Failed to get quick stats: $e');
    }
  }

  // Audit Trail
  static Future<void> _logAuditTrail({
    required String adminId,
    required String action,
    required String targetType,
    required String targetId,
    Map<String, dynamic>? details,
  }) async {
    try {
      final auditLog = {
        'adminId': adminId,
        'action': action,
        'targetType': targetType,
        'targetId': targetId,
        'details': details ?? {},
        'timestamp': Timestamp.now(),
        'ipAddress': '127.0.0.1', // TODO: Get actual IP
        'userAgent': 'Admin Panel', // TODO: Get actual user agent
      };

      await _firestore.collection('audit_logs').add(auditLog);
    } catch (e) {
      // Don't throw error for audit logging failures
      print('Failed to log audit trail: $e');
    }
  }

  // AI-Powered Content Analysis
  static Future<Map<String, dynamic>> analyzeContent(String contentId) async {
    try {
      // TODO: Integrate with AI service for content analysis
      // This is a placeholder implementation
      
      final content = await _firestore.collection('content').doc(contentId).get();
      if (!content.exists) {
        throw Exception('Content not found');
      }

      final contentData = content.data()!;
      
      // Simulate AI analysis
      final analysis = {
        'toxicityScore': 0.15, // 0-1 scale
        'inappropriateContent': false,
        'spamProbability': 0.05,
        'aiConfidence': 0.85,
        'flags': ['gaming_content', 'appropriate_language'],
        'recommendation': 'approve',
        'reason': 'Content appears to be appropriate gaming content',
      };

      return analysis;
    } catch (e) {
      throw Exception('Failed to analyze content: $e');
    }
  }

  // User Behavior Analysis
  static Future<Map<String, dynamic>> analyzeUserBehavior(String userId) async {
    try {
      // TODO: Integrate with AI service for user behavior analysis
      // This is a placeholder implementation

      final user = await _firestore.collection('users').doc(userId).get();
      if (!user.exists) {
        throw Exception('User not found');
      }

      final userData = user.data()!;
      
      // Get user's recent actions
      final recentActions = await _firestore
          .collection('admin_actions')
          .where('targetUserId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(10)
          .get();

      // Simulate behavior analysis
      final analysis = {
        'toxicityScore': 0.25, // 0-1 scale
        'riskLevel': 'low', // low, medium, high, critical
        'warningCount': recentActions.docs.length,
        'lastViolation': recentActions.docs.isNotEmpty 
            ? recentActions.docs.first.data()['timestamp'] 
            : null,
        'recommendation': 'monitor',
        'flags': ['normal_behavior'],
      };

      return analysis;
    } catch (e) {
      throw Exception('Failed to analyze user behavior: $e');
    }
  }

  // Bulk Operations
  static Future<void> bulkModerateContent({
    required List<String> contentIds,
    required String adminId,
    required ContentAction action,
    required String reason,
  }) async {
    try {
      final batch = _firestore.batch();
      
      for (final contentId in contentIds) {
        final moderationAction = ContentModerationAction(
          id: '${DateTime.now().millisecondsSinceEpoch}_$contentId',
          contentId: contentId,
          adminId: adminId,
          contentType: ContentType.post, // Default, should be determined per content
          action: action,
          reason: reason,
          timestamp: DateTime.now(),
        );

        batch.set(
          _firestore.collection('content_moderation').doc(moderationAction.id),
          moderationAction.toFirestore(),
        );

        batch.update(
          _firestore.collection('content').doc(contentId),
          {
            'moderationStatus': action.toString().split('.').last,
            'moderatedAt': Timestamp.now(),
            'moderatedBy': adminId,
            'moderationReason': reason,
          },
        );
      }

      await batch.commit();

      await _logAuditTrail(
        adminId: adminId,
        action: 'bulk_content_moderation',
        targetType: 'content',
        targetId: contentIds.join(','),
        details: {
          'action': action.toString().split('.').last,
          'reason': reason,
          'count': contentIds.length,
        },
      );
    } catch (e) {
      throw Exception('Failed to bulk moderate content: $e');
    }
  }
}
