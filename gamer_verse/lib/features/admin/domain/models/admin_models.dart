import 'package:cloud_firestore/cloud_firestore.dart';

// Admin Roles and Permissions
enum AdminRole { admin, moderator, content_moderator, esports_moderator, support }
enum UserAction { ban, suspend, warn, verify, promote, demote, shadow_ban }
enum ContentAction { approve, remove, flag, highlight, feature }
enum BanType { temporary, permanent, shadow }
enum ContentType { post, reel, story, live_stream, comment, clan }
enum ReportType { abuse, harassment, cheating, inappropriate, spam, fake_account }
enum TournamentStatus { pending, approved, rejected, active, completed, cancelled }

// Admin User Model
class AdminUser {
  final String id;
  final String email;
  final String username;
  final String? displayName;
  final String? photoURL;
  final AdminRole role;
  final List<String> permissions;
  final DateTime createdAt;
  final DateTime lastActive;
  final bool isActive;
  final Map<String, dynamic> activityLog;
  final String? assignedRegion;
  final List<String> managedGames;
  final int actionsTaken;
  final double performanceScore;

  AdminUser({
    required this.id,
    required this.email,
    required this.username,
    this.displayName,
    this.photoURL,
    required this.role,
    required this.permissions,
    required this.createdAt,
    required this.lastActive,
    this.isActive = true,
    this.activityLog = const {},
    this.assignedRegion,
    this.managedGames = const [],
    this.actionsTaken = 0,
    this.performanceScore = 0.0,
  });

  factory AdminUser.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminUser(
      id: doc.id,
      email: data['email'] ?? '',
      username: data['username'] ?? '',
      displayName: data['displayName'],
      photoURL: data['photoURL'],
      role: AdminRole.values.firstWhere(
        (e) => e.toString() == 'AdminRole.${data['role'] ?? 'moderator'}',
        orElse: () => AdminRole.moderator,
      ),
      permissions: List<String>.from(data['permissions'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastActive: (data['lastActive'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
      activityLog: Map<String, dynamic>.from(data['activityLog'] ?? {}),
      assignedRegion: data['assignedRegion'],
      managedGames: List<String>.from(data['managedGames'] ?? []),
      actionsTaken: data['actionsTaken'] ?? 0,
      performanceScore: (data['performanceScore'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'username': username,
      'displayName': displayName,
      'photoURL': photoURL,
      'role': role.toString().split('.').last,
      'permissions': permissions,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastActive': Timestamp.fromDate(lastActive),
      'isActive': isActive,
      'activityLog': activityLog,
      'assignedRegion': assignedRegion,
      'managedGames': managedGames,
      'actionsTaken': actionsTaken,
      'performanceScore': performanceScore,
    };
  }

  AdminUser copyWith({
    String? id,
    String? email,
    String? username,
    String? displayName,
    String? photoURL,
    AdminRole? role,
    List<String>? permissions,
    DateTime? createdAt,
    DateTime? lastActive,
    bool? isActive,
    Map<String, dynamic>? activityLog,
    String? assignedRegion,
    List<String>? managedGames,
    int? actionsTaken,
    double? performanceScore,
  }) {
    return AdminUser(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
      isActive: isActive ?? this.isActive,
      activityLog: activityLog ?? this.activityLog,
      assignedRegion: assignedRegion ?? this.assignedRegion,
      managedGames: managedGames ?? this.managedGames,
      actionsTaken: actionsTaken ?? this.actionsTaken,
      performanceScore: performanceScore ?? this.performanceScore,
    );
  }
}

// User Management Model
class UserManagementAction {
  final String id;
  final String targetUserId;
  final String adminId;
  final UserAction action;
  final String reason;
  final Map<String, dynamic> details;
  final DateTime timestamp;
  final DateTime? expiresAt;
  final bool isActive;
  final String? appealId;
  final Map<String, dynamic> evidence;

  UserManagementAction({
    required this.id,
    required this.targetUserId,
    required this.adminId,
    required this.action,
    required this.reason,
    this.details = const {},
    required this.timestamp,
    this.expiresAt,
    this.isActive = true,
    this.appealId,
    this.evidence = const {},
  });

  factory UserManagementAction.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserManagementAction(
      id: doc.id,
      targetUserId: data['targetUserId'] ?? '',
      adminId: data['adminId'] ?? '',
      action: UserAction.values.firstWhere(
        (e) => e.toString() == 'UserAction.${data['action']}',
        orElse: () => UserAction.warn,
      ),
      reason: data['reason'] ?? '',
      details: Map<String, dynamic>.from(data['details'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      expiresAt: data['expiresAt'] != null ? (data['expiresAt'] as Timestamp).toDate() : null,
      isActive: data['isActive'] ?? true,
      appealId: data['appealId'],
      evidence: Map<String, dynamic>.from(data['evidence'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'targetUserId': targetUserId,
      'adminId': adminId,
      'action': action.toString().split('.').last,
      'reason': reason,
      'details': details,
      'timestamp': Timestamp.fromDate(timestamp),
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
      'isActive': isActive,
      'appealId': appealId,
      'evidence': evidence,
    };
  }
}

// Content Moderation Model
class ContentModerationAction {
  final String id;
  final String contentId;
  final String adminId;
  final ContentType contentType;
  final ContentAction action;
  final String reason;
  final Map<String, dynamic> details;
  final DateTime timestamp;
  final bool isActive;
  final double aiConfidence;
  final Map<String, dynamic> flags;

  ContentModerationAction({
    required this.id,
    required this.contentId,
    required this.adminId,
    required this.contentType,
    required this.action,
    required this.reason,
    this.details = const {},
    required this.timestamp,
    this.isActive = true,
    this.aiConfidence = 0.0,
    this.flags = const {},
  });

  factory ContentModerationAction.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ContentModerationAction(
      id: doc.id,
      contentId: data['contentId'] ?? '',
      adminId: data['adminId'] ?? '',
      contentType: ContentType.values.firstWhere(
        (e) => e.toString() == 'ContentType.${data['contentType']}',
        orElse: () => ContentType.post,
      ),
      action: ContentAction.values.firstWhere(
        (e) => e.toString() == 'ContentAction.${data['action']}',
        orElse: () => ContentAction.approve,
      ),
      reason: data['reason'] ?? '',
      details: Map<String, dynamic>.from(data['details'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
      aiConfidence: (data['aiConfidence'] ?? 0.0).toDouble(),
      flags: Map<String, dynamic>.from(data['flags'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'contentId': contentId,
      'adminId': adminId,
      'contentType': contentType.toString().split('.').last,
      'action': action.toString().split('.').last,
      'reason': reason,
      'details': details,
      'timestamp': Timestamp.fromDate(timestamp),
      'isActive': isActive,
      'aiConfidence': aiConfidence,
      'flags': flags,
    };
  }
}

// User Report Model
class UserReport {
  final String id;
  final String reporterId;
  final String reportedUserId;
  final String? reportedContentId;
  final ReportType reportType;
  final String description;
  final Map<String, dynamic> evidence;
  final DateTime timestamp;
  final String status; // pending, investigating, resolved, dismissed
  final String? assignedAdminId;
  final DateTime? resolvedAt;
  final String? resolution;
  final int severity; // 1-5 scale

  UserReport({
    required this.id,
    required this.reporterId,
    required this.reportedUserId,
    this.reportedContentId,
    required this.reportType,
    required this.description,
    this.evidence = const {},
    required this.timestamp,
    this.status = 'pending',
    this.assignedAdminId,
    this.resolvedAt,
    this.resolution,
    this.severity = 1,
  });

  factory UserReport.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserReport(
      id: doc.id,
      reporterId: data['reporterId'] ?? '',
      reportedUserId: data['reportedUserId'] ?? '',
      reportedContentId: data['reportedContentId'],
      reportType: ReportType.values.firstWhere(
        (e) => e.toString() == 'ReportType.${data['reportType']}',
        orElse: () => ReportType.spam,
      ),
      description: data['description'] ?? '',
      evidence: Map<String, dynamic>.from(data['evidence'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      status: data['status'] ?? 'pending',
      assignedAdminId: data['assignedAdminId'],
      resolvedAt: data['resolvedAt'] != null ? (data['resolvedAt'] as Timestamp).toDate() : null,
      resolution: data['resolution'],
      severity: data['severity'] ?? 1,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'reporterId': reporterId,
      'reportedUserId': reportedUserId,
      'reportedContentId': reportedContentId,
      'reportType': reportType.toString().split('.').last,
      'description': description,
      'evidence': evidence,
      'timestamp': Timestamp.fromDate(timestamp),
      'status': status,
      'assignedAdminId': assignedAdminId,
      'resolvedAt': resolvedAt != null ? Timestamp.fromDate(resolvedAt!) : null,
      'resolution': resolution,
      'severity': severity,
    };
  }
}

// Tournament Management Model
class TournamentManagement {
  final String id;
  final String tournamentId;
  final String adminId;
  final TournamentStatus status;
  final String reason;
  final Map<String, dynamic> details;
  final DateTime timestamp;
  final DateTime? approvedAt;
  final String? assignedModeratorId;
  final Map<String, dynamic> settings;

  TournamentManagement({
    required this.id,
    required this.tournamentId,
    required this.adminId,
    required this.status,
    required this.reason,
    this.details = const {},
    required this.timestamp,
    this.approvedAt,
    this.assignedModeratorId,
    this.settings = const {},
  });

  factory TournamentManagement.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TournamentManagement(
      id: doc.id,
      tournamentId: data['tournamentId'] ?? '',
      adminId: data['adminId'] ?? '',
      status: TournamentStatus.values.firstWhere(
        (e) => e.toString() == 'TournamentStatus.${data['status']}',
        orElse: () => TournamentStatus.pending,
      ),
      reason: data['reason'] ?? '',
      details: Map<String, dynamic>.from(data['details'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      approvedAt: data['approvedAt'] != null ? (data['approvedAt'] as Timestamp).toDate() : null,
      assignedModeratorId: data['assignedModeratorId'],
      settings: Map<String, dynamic>.from(data['settings'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'tournamentId': tournamentId,
      'adminId': adminId,
      'status': status.toString().split('.').last,
      'reason': reason,
      'details': details,
      'timestamp': Timestamp.fromDate(timestamp),
      'approvedAt': approvedAt != null ? Timestamp.fromDate(approvedAt!) : null,
      'assignedModeratorId': assignedModeratorId,
      'settings': settings,
    };
  }
}

// Analytics Model
class AdminAnalytics {
  final String id;
  final DateTime date;
  final Map<String, int> userActions;
  final Map<String, int> contentActions;
  final Map<String, int> reports;
  final Map<String, int> tournaments;
  final int activeUsers;
  final int newUsers;
  final int bannedUsers;
  final double averageResponseTime;
  final Map<String, dynamic> gameStats;

  AdminAnalytics({
    required this.id,
    required this.date,
    this.userActions = const {},
    this.contentActions = const {},
    this.reports = const {},
    this.tournaments = const {},
    this.activeUsers = 0,
    this.newUsers = 0,
    this.bannedUsers = 0,
    this.averageResponseTime = 0.0,
    this.gameStats = const {},
  });

  factory AdminAnalytics.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AdminAnalytics(
      id: doc.id,
      date: (data['date'] as Timestamp).toDate(),
      userActions: Map<String, int>.from(data['userActions'] ?? {}),
      contentActions: Map<String, int>.from(data['contentActions'] ?? {}),
      reports: Map<String, int>.from(data['reports'] ?? {}),
      tournaments: Map<String, int>.from(data['tournaments'] ?? {}),
      activeUsers: data['activeUsers'] ?? 0,
      newUsers: data['newUsers'] ?? 0,
      bannedUsers: data['bannedUsers'] ?? 0,
      averageResponseTime: (data['averageResponseTime'] ?? 0.0).toDouble(),
      gameStats: Map<String, dynamic>.from(data['gameStats'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'date': Timestamp.fromDate(date),
      'userActions': userActions,
      'contentActions': contentActions,
      'reports': reports,
      'tournaments': tournaments,
      'activeUsers': activeUsers,
      'newUsers': newUsers,
      'bannedUsers': bannedUsers,
      'averageResponseTime': averageResponseTime,
      'gameStats': gameStats,
    };
  }
}
