import 'package:cloud_firestore/cloud_firestore.dart';

enum GamingPlatform { pc, ps5, xbox, mobile, switch }
enum UserStatus { online, offline, inGame, streaming, away }
enum UserRole { user, creator, moderator, admin }
enum SubscriptionTier { free, premium, pro, elite }

class UserModel {
  final String id;
  final String email;
  final String username;
  final String? displayName;
  final String? photoURL;
  final String? bio;
  final String? gamertag;
  final List<String> favoriteGames;
  final List<GamingPlatform> platforms;
  final Map<String, String> platformIds; // Steam ID, PSN ID, Xbox ID, etc.
  final List<String> followers;
  final List<String> following;
  final List<String> squads;
  final List<String> achievements;
  final Map<String, dynamic> stats;
  final int xp;
  final int level;
  final UserStatus status;
  final UserRole role;
  final SubscriptionTier subscriptionTier;
  final bool isVerified;
  final bool isPrivate;
  final bool isStreaming;
  final String? currentGame;
  final String? streamTitle;
  final String? streamUrl;
  final DateTime createdAt;
  final DateTime lastSeen;
  final DateTime? lastStreamed;
  final Map<String, dynamic>? settings;
  final String? fcmToken;
  final List<String> badges;
  final Map<String, int> gameStats; // Game-specific statistics
  final List<String> tournaments;
  final Map<String, dynamic>? socialLinks;
  final String? clanId;
  final String? clanRole;
  final List<String> blockedUsers;
  final Map<String, dynamic>? preferences;
  final bool isOnline;
  final String? statusMessage;
  final List<String> favoriteCreators;
  final Map<String, dynamic>? monetizationSettings;

  UserModel({
    required this.id,
    required this.email,
    required this.username,
    this.displayName,
    this.photoURL,
    this.bio,
    this.gamertag,
    this.favoriteGames = const [],
    this.platforms = const [],
    this.platformIds = const {},
    this.followers = const [],
    this.following = const [],
    this.squads = const [],
    this.achievements = const [],
    this.stats = const {},
    this.xp = 0,
    this.level = 1,
    this.status = UserStatus.offline,
    this.role = UserRole.user,
    this.subscriptionTier = SubscriptionTier.free,
    this.isVerified = false,
    this.isPrivate = false,
    this.isStreaming = false,
    this.currentGame,
    this.streamTitle,
    this.streamUrl,
    required this.createdAt,
    required this.lastSeen,
    this.lastStreamed,
    this.settings,
    this.fcmToken,
    this.badges = const [],
    this.gameStats = const {},
    this.tournaments = const [],
    this.socialLinks,
    this.clanId,
    this.clanRole,
    this.blockedUsers = const [],
    this.preferences,
    this.isOnline = false,
    this.statusMessage,
    this.favoriteCreators = const [],
    this.monetizationSettings,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      email: data['email'] ?? '',
      username: data['username'] ?? '',
      displayName: data['displayName'],
      photoURL: data['photoURL'],
      bio: data['bio'],
      gamertag: data['gamertag'],
      favoriteGames: List<String>.from(data['favoriteGames'] ?? []),
      platforms: (data['platforms'] as List<dynamic>?)
              ?.map((e) => GamingPlatform.values.firstWhere(
                    (platform) => platform.toString() == 'GamingPlatform.$e',
                    orElse: () => GamingPlatform.pc,
                  ))
              .toList() ??
          [],
      platformIds: Map<String, String>.from(data['platformIds'] ?? {}),
      followers: List<String>.from(data['followers'] ?? []),
      following: List<String>.from(data['following'] ?? []),
      squads: List<String>.from(data['squads'] ?? []),
      achievements: List<String>.from(data['achievements'] ?? []),
      stats: Map<String, dynamic>.from(data['stats'] ?? {}),
      xp: data['xp'] ?? 0,
      level: data['level'] ?? 1,
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == 'UserStatus.${data['status'] ?? 'offline'}',
        orElse: () => UserStatus.offline,
      ),
      role: UserRole.values.firstWhere(
        (e) => e.toString() == 'UserRole.${data['role'] ?? 'user'}',
        orElse: () => UserRole.user,
      ),
      subscriptionTier: SubscriptionTier.values.firstWhere(
        (e) => e.toString() == 'SubscriptionTier.${data['subscriptionTier'] ?? 'free'}',
        orElse: () => SubscriptionTier.free,
      ),
      isVerified: data['isVerified'] ?? false,
      isPrivate: data['isPrivate'] ?? false,
      isStreaming: data['isStreaming'] ?? false,
      currentGame: data['currentGame'],
      streamTitle: data['streamTitle'],
      streamUrl: data['streamUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastSeen: (data['lastSeen'] as Timestamp).toDate(),
      lastStreamed: data['lastStreamed'] != null
          ? (data['lastStreamed'] as Timestamp).toDate()
          : null,
      settings: data['settings'],
      fcmToken: data['fcmToken'],
      badges: List<String>.from(data['badges'] ?? []),
      gameStats: Map<String, int>.from(data['gameStats'] ?? {}),
      tournaments: List<String>.from(data['tournaments'] ?? []),
      socialLinks: data['socialLinks'],
      clanId: data['clanId'],
      clanRole: data['clanRole'],
      blockedUsers: List<String>.from(data['blockedUsers'] ?? []),
      preferences: data['preferences'],
      isOnline: data['isOnline'] ?? false,
      statusMessage: data['statusMessage'],
      favoriteCreators: List<String>.from(data['favoriteCreators'] ?? []),
      monetizationSettings: data['monetizationSettings'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'username': username,
      'displayName': displayName,
      'photoURL': photoURL,
      'bio': bio,
      'gamertag': gamertag,
      'favoriteGames': favoriteGames,
      'platforms': platforms.map((e) => e.toString().split('.').last).toList(),
      'platformIds': platformIds,
      'followers': followers,
      'following': following,
      'squads': squads,
      'achievements': achievements,
      'stats': stats,
      'xp': xp,
      'level': level,
      'status': status.toString().split('.').last,
      'role': role.toString().split('.').last,
      'subscriptionTier': subscriptionTier.toString().split('.').last,
      'isVerified': isVerified,
      'isPrivate': isPrivate,
      'isStreaming': isStreaming,
      'currentGame': currentGame,
      'streamTitle': streamTitle,
      'streamUrl': streamUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastSeen': Timestamp.fromDate(lastSeen),
      'lastStreamed': lastStreamed != null ? Timestamp.fromDate(lastStreamed!) : null,
      'settings': settings,
      'fcmToken': fcmToken,
      'badges': badges,
      'gameStats': gameStats,
      'tournaments': tournaments,
      'socialLinks': socialLinks,
      'clanId': clanId,
      'clanRole': clanRole,
      'blockedUsers': blockedUsers,
      'preferences': preferences,
      'isOnline': isOnline,
      'statusMessage': statusMessage,
      'favoriteCreators': favoriteCreators,
      'monetizationSettings': monetizationSettings,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? username,
    String? displayName,
    String? photoURL,
    String? bio,
    String? gamertag,
    List<String>? favoriteGames,
    List<GamingPlatform>? platforms,
    Map<String, String>? platformIds,
    List<String>? followers,
    List<String>? following,
    List<String>? squads,
    List<String>? achievements,
    Map<String, dynamic>? stats,
    int? xp,
    int? level,
    UserStatus? status,
    UserRole? role,
    SubscriptionTier? subscriptionTier,
    bool? isVerified,
    bool? isPrivate,
    bool? isStreaming,
    String? currentGame,
    String? streamTitle,
    String? streamUrl,
    DateTime? createdAt,
    DateTime? lastSeen,
    DateTime? lastStreamed,
    Map<String, dynamic>? settings,
    String? fcmToken,
    List<String>? badges,
    Map<String, int>? gameStats,
    List<String>? tournaments,
    Map<String, dynamic>? socialLinks,
    String? clanId,
    String? clanRole,
    List<String>? blockedUsers,
    Map<String, dynamic>? preferences,
    bool? isOnline,
    String? statusMessage,
    List<String>? favoriteCreators,
    Map<String, dynamic>? monetizationSettings,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      bio: bio ?? this.bio,
      gamertag: gamertag ?? this.gamertag,
      favoriteGames: favoriteGames ?? this.favoriteGames,
      platforms: platforms ?? this.platforms,
      platformIds: platformIds ?? this.platformIds,
      followers: followers ?? this.followers,
      following: following ?? this.following,
      squads: squads ?? this.squads,
      achievements: achievements ?? this.achievements,
      stats: stats ?? this.stats,
      xp: xp ?? this.xp,
      level: level ?? this.level,
      status: status ?? this.status,
      role: role ?? this.role,
      subscriptionTier: subscriptionTier ?? this.subscriptionTier,
      isVerified: isVerified ?? this.isVerified,
      isPrivate: isPrivate ?? this.isPrivate,
      isStreaming: isStreaming ?? this.isStreaming,
      currentGame: currentGame ?? this.currentGame,
      streamTitle: streamTitle ?? this.streamTitle,
      streamUrl: streamUrl ?? this.streamUrl,
      createdAt: createdAt ?? this.createdAt,
      lastSeen: lastSeen ?? this.lastSeen,
      lastStreamed: lastStreamed ?? this.lastStreamed,
      settings: settings ?? this.settings,
      fcmToken: fcmToken ?? this.fcmToken,
      badges: badges ?? this.badges,
      gameStats: gameStats ?? this.gameStats,
      tournaments: tournaments ?? this.tournaments,
      socialLinks: socialLinks ?? this.socialLinks,
      clanId: clanId ?? this.clanId,
      clanRole: clanRole ?? this.clanRole,
      blockedUsers: blockedUsers ?? this.blockedUsers,
      preferences: preferences ?? this.preferences,
      isOnline: isOnline ?? this.isOnline,
      statusMessage: statusMessage ?? this.statusMessage,
      favoriteCreators: favoriteCreators ?? this.favoriteCreators,
      monetizationSettings: monetizationSettings ?? this.monetizationSettings,
    );
  }

  // Computed properties
  int get followerCount => followers.length;
  int get followingCount => following.length;
  int get postCount => stats['postCount'] ?? 0;
  int get totalLikes => stats['totalLikes'] ?? 0;
  int get totalViews => stats['totalViews'] ?? 0;
  bool get isPremium => subscriptionTier != SubscriptionTier.free;
  bool get isCreator => role == UserRole.creator;
  bool get isModerator => role == UserRole.moderator || role == UserRole.admin;

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, gamertag: $gamertag, level: $level)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
