import 'package:cloud_firestore/cloud_firestore.dart';

// Super Admin Roles and Permissions
enum SuperAdminRole { super_admin, platform_admin, financial_admin, security_admin }
enum PermissionLevel { read, write, admin, super_admin }
enum SubscriptionTier { free, premium, pro, elite, enterprise }
enum MonetizationType { subscription, in_app_purchase, advertising, sponsorship, donations }
enum SecurityLevel { low, medium, high, critical }
enum ComplianceType { gdpr, ccpa, coppa, custom }

// Super Admin User Model
class SuperAdminUser {
  final String id;
  final String email;
  final String username;
  final String? displayName;
  final String? photoURL;
  final SuperAdminRole role;
  final List<String> permissions;
  final Map<String, PermissionLevel> granularPermissions;
  final DateTime createdAt;
  final DateTime lastActive;
  final bool isActive;
  final Map<String, dynamic> activityLog;
  final String? assignedRegion;
  final List<String> managedFeatures;
  final int actionsTaken;
  final double performanceScore;
  final String? twoFactorSecret;
  final DateTime? lastSecurityAudit;

  SuperAdminUser({
    required this.id,
    required this.email,
    required this.username,
    this.displayName,
    this.photoURL,
    required this.role,
    required this.permissions,
    this.granularPermissions = const {},
    required this.createdAt,
    required this.lastActive,
    this.isActive = true,
    this.activityLog = const {},
    this.assignedRegion,
    this.managedFeatures = const [],
    this.actionsTaken = 0,
    this.performanceScore = 0.0,
    this.twoFactorSecret,
    this.lastSecurityAudit,
  });

  factory SuperAdminUser.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SuperAdminUser(
      id: doc.id,
      email: data['email'] ?? '',
      username: data['username'] ?? '',
      displayName: data['displayName'],
      photoURL: data['photoURL'],
      role: SuperAdminRole.values.firstWhere(
        (e) => e.toString() == 'SuperAdminRole.${data['role'] ?? 'platform_admin'}',
        orElse: () => SuperAdminRole.platform_admin,
      ),
      permissions: List<String>.from(data['permissions'] ?? []),
      granularPermissions: Map<String, PermissionLevel>.from(
        (data['granularPermissions'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, PermissionLevel.values.firstWhere(
            (e) => e.toString() == 'PermissionLevel.$value',
            orElse: () => PermissionLevel.read,
          )),
        ) ?? {},
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastActive: (data['lastActive'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
      activityLog: Map<String, dynamic>.from(data['activityLog'] ?? {}),
      assignedRegion: data['assignedRegion'],
      managedFeatures: List<String>.from(data['managedFeatures'] ?? []),
      actionsTaken: data['actionsTaken'] ?? 0,
      performanceScore: (data['performanceScore'] ?? 0.0).toDouble(),
      twoFactorSecret: data['twoFactorSecret'],
      lastSecurityAudit: data['lastSecurityAudit'] != null 
          ? (data['lastSecurityAudit'] as Timestamp).toDate() 
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'username': username,
      'displayName': displayName,
      'photoURL': photoURL,
      'role': role.toString().split('.').last,
      'permissions': permissions,
      'granularPermissions': granularPermissions.map(
        (key, value) => MapEntry(key, value.toString().split('.').last),
      ),
      'createdAt': Timestamp.fromDate(createdAt),
      'lastActive': Timestamp.fromDate(lastActive),
      'isActive': isActive,
      'activityLog': activityLog,
      'assignedRegion': assignedRegion,
      'managedFeatures': managedFeatures,
      'actionsTaken': actionsTaken,
      'performanceScore': performanceScore,
      'twoFactorSecret': twoFactorSecret,
      'lastSecurityAudit': lastSecurityAudit != null 
          ? Timestamp.fromDate(lastSecurityAudit!) 
          : null,
    };
  }
}

// Platform Settings Model
class PlatformSettings {
  final String id;
  final Map<String, dynamic> xpSystem;
  final Map<String, dynamic> gamification;
  final Map<String, dynamic> toxicityFilter;
  final Map<String, dynamic> contentModeration;
  final Map<String, dynamic> security;
  final Map<String, dynamic> compliance;
  final Map<String, dynamic> monetization;
  final Map<String, dynamic> integrations;
  final DateTime lastUpdated;
  final String updatedBy;

  PlatformSettings({
    required this.id,
    this.xpSystem = const {},
    this.gamification = const {},
    this.toxicityFilter = const {},
    this.contentModeration = const {},
    this.security = const {},
    this.compliance = const {},
    this.monetization = const {},
    this.integrations = const {},
    required this.lastUpdated,
    required this.updatedBy,
  });

  factory PlatformSettings.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PlatformSettings(
      id: doc.id,
      xpSystem: Map<String, dynamic>.from(data['xpSystem'] ?? {}),
      gamification: Map<String, dynamic>.from(data['gamification'] ?? {}),
      toxicityFilter: Map<String, dynamic>.from(data['toxicityFilter'] ?? {}),
      contentModeration: Map<String, dynamic>.from(data['contentModeration'] ?? {}),
      security: Map<String, dynamic>.from(data['security'] ?? {}),
      compliance: Map<String, dynamic>.from(data['compliance'] ?? {}),
      monetization: Map<String, dynamic>.from(data['monetization'] ?? {}),
      integrations: Map<String, dynamic>.from(data['integrations'] ?? {}),
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
      updatedBy: data['updatedBy'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'xpSystem': xpSystem,
      'gamification': gamification,
      'toxicityFilter': toxicityFilter,
      'contentModeration': contentModeration,
      'security': security,
      'compliance': compliance,
      'monetization': monetization,
      'integrations': integrations,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'updatedBy': updatedBy,
    };
  }
}

// Monetization Model
class MonetizationSettings {
  final String id;
  final Map<String, SubscriptionTier> subscriptionTiers;
  final Map<String, dynamic> pricing;
  final Map<String, dynamic> storeItems;
  final Map<String, dynamic> advertising;
  final Map<String, dynamic> sponsorships;
  final Map<String, dynamic> donations;
  final Map<String, dynamic> revenueSharing;
  final DateTime lastUpdated;
  final String updatedBy;

  MonetizationSettings({
    required this.id,
    this.subscriptionTiers = const {},
    this.pricing = const {},
    this.storeItems = const {},
    this.advertising = const {},
    this.sponsorships = const {},
    this.donations = const {},
    this.revenueSharing = const {},
    required this.lastUpdated,
    required this.updatedBy,
  });

  factory MonetizationSettings.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return MonetizationSettings(
      id: doc.id,
      subscriptionTiers: Map<String, SubscriptionTier>.from(
        (data['subscriptionTiers'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, SubscriptionTier.values.firstWhere(
            (e) => e.toString() == 'SubscriptionTier.$value',
            orElse: () => SubscriptionTier.free,
          )),
        ) ?? {},
      ),
      pricing: Map<String, dynamic>.from(data['pricing'] ?? {}),
      storeItems: Map<String, dynamic>.from(data['storeItems'] ?? {}),
      advertising: Map<String, dynamic>.from(data['advertising'] ?? {}),
      sponsorships: Map<String, dynamic>.from(data['sponsorships'] ?? {}),
      donations: Map<String, dynamic>.from(data['donations'] ?? {}),
      revenueSharing: Map<String, dynamic>.from(data['revenueSharing'] ?? {}),
      lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
      updatedBy: data['updatedBy'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'subscriptionTiers': subscriptionTiers.map(
        (key, value) => MapEntry(key, value.toString().split('.').last),
      ),
      'pricing': pricing,
      'storeItems': storeItems,
      'advertising': advertising,
      'sponsorships': sponsorships,
      'donations': donations,
      'revenueSharing': revenueSharing,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'updatedBy': updatedBy,
    };
  }
}

// Financial Analytics Model
class FinancialAnalytics {
  final String id;
  final DateTime date;
  final Map<String, double> revenue;
  final Map<String, int> subscriptions;
  final Map<String, double> inAppPurchases;
  final Map<String, double> advertising;
  final Map<String, double> sponsorships;
  final Map<String, double> donations;
  final Map<String, double> expenses;
  final double totalRevenue;
  final double totalExpenses;
  final double profit;
  final Map<String, dynamic> regionalStats;
  final Map<String, dynamic> gameStats;

  FinancialAnalytics({
    required this.id,
    required this.date,
    this.revenue = const {},
    this.subscriptions = const {},
    this.inAppPurchases = const {},
    this.advertising = const {},
    this.sponsorships = const {},
    this.donations = const {},
    this.expenses = const {},
    this.totalRevenue = 0.0,
    this.totalExpenses = 0.0,
    this.profit = 0.0,
    this.regionalStats = const {},
    this.gameStats = const {},
  });

  factory FinancialAnalytics.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return FinancialAnalytics(
      id: doc.id,
      date: (data['date'] as Timestamp).toDate(),
      revenue: Map<String, double>.from(data['revenue'] ?? {}),
      subscriptions: Map<String, int>.from(data['subscriptions'] ?? {}),
      inAppPurchases: Map<String, double>.from(data['inAppPurchases'] ?? {}),
      advertising: Map<String, double>.from(data['advertising'] ?? {}),
      sponsorships: Map<String, double>.from(data['sponsorships'] ?? {}),
      donations: Map<String, double>.from(data['donations'] ?? {}),
      expenses: Map<String, double>.from(data['expenses'] ?? {}),
      totalRevenue: (data['totalRevenue'] ?? 0.0).toDouble(),
      totalExpenses: (data['totalExpenses'] ?? 0.0).toDouble(),
      profit: (data['profit'] ?? 0.0).toDouble(),
      regionalStats: Map<String, dynamic>.from(data['regionalStats'] ?? {}),
      gameStats: Map<String, dynamic>.from(data['gameStats'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'date': Timestamp.fromDate(date),
      'revenue': revenue,
      'subscriptions': subscriptions,
      'inAppPurchases': inAppPurchases,
      'advertising': advertising,
      'sponsorships': sponsorships,
      'donations': donations,
      'expenses': expenses,
      'totalRevenue': totalRevenue,
      'totalExpenses': totalExpenses,
      'profit': profit,
      'regionalStats': regionalStats,
      'gameStats': gameStats,
    };
  }
}

// Security & Compliance Model
class SecurityCompliance {
  final String id;
  final Map<String, SecurityLevel> securityLevels;
  final Map<String, ComplianceType> complianceTypes;
  final Map<String, dynamic> auditLogs;
  final Map<String, dynamic> fraudDetection;
  final Map<String, dynamic> dataProtection;
  final Map<String, dynamic> apiKeys;
  final Map<String, dynamic> accessControls;
  final DateTime lastSecurityAudit;
  final DateTime lastComplianceCheck;
  final String lastAuditedBy;

  SecurityCompliance({
    required this.id,
    this.securityLevels = const {},
    this.complianceTypes = const {},
    this.auditLogs = const {},
    this.fraudDetection = const {},
    this.dataProtection = const {},
    this.apiKeys = const {},
    this.accessControls = const {},
    required this.lastSecurityAudit,
    required this.lastComplianceCheck,
    required this.lastAuditedBy,
  });

  factory SecurityCompliance.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SecurityCompliance(
      id: doc.id,
      securityLevels: Map<String, SecurityLevel>.from(
        (data['securityLevels'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, SecurityLevel.values.firstWhere(
            (e) => e.toString() == 'SecurityLevel.$value',
            orElse: () => SecurityLevel.medium,
          )),
        ) ?? {},
      ),
      complianceTypes: Map<String, ComplianceType>.from(
        (data['complianceTypes'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, ComplianceType.values.firstWhere(
            (e) => e.toString() == 'ComplianceType.$value',
            orElse: () => ComplianceType.gdpr,
          )),
        ) ?? {},
      ),
      auditLogs: Map<String, dynamic>.from(data['auditLogs'] ?? {}),
      fraudDetection: Map<String, dynamic>.from(data['fraudDetection'] ?? {}),
      dataProtection: Map<String, dynamic>.from(data['dataProtection'] ?? {}),
      apiKeys: Map<String, dynamic>.from(data['apiKeys'] ?? {}),
      accessControls: Map<String, dynamic>.from(data['accessControls'] ?? {}),
      lastSecurityAudit: (data['lastSecurityAudit'] as Timestamp).toDate(),
      lastComplianceCheck: (data['lastComplianceCheck'] as Timestamp).toDate(),
      lastAuditedBy: data['lastAuditedBy'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'securityLevels': securityLevels.map(
        (key, value) => MapEntry(key, value.toString().split('.').last),
      ),
      'complianceTypes': complianceTypes.map(
        (key, value) => MapEntry(key, value.toString().split('.').last),
      ),
      'auditLogs': auditLogs,
      'fraudDetection': fraudDetection,
      'dataProtection': dataProtection,
      'apiKeys': apiKeys,
      'accessControls': accessControls,
      'lastSecurityAudit': Timestamp.fromDate(lastSecurityAudit),
      'lastComplianceCheck': Timestamp.fromDate(lastComplianceCheck),
      'lastAuditedBy': lastAuditedBy,
    };
  }
}

// Global Event Model
class GlobalEvent {
  final String id;
  final String title;
  final String description;
  final String eventType; // tournament, announcement, maintenance, etc.
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic> settings;
  final List<String> assignedAdmins;
  final String status; // planned, active, completed, cancelled
  final Map<String, dynamic> sponsors;
  final double prizePool;
  final String createdBy;
  final DateTime createdAt;

  GlobalEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.eventType,
    required this.startDate,
    required this.endDate,
    this.settings = const {},
    this.assignedAdmins = const [],
    this.status = 'planned',
    this.sponsors = const {},
    this.prizePool = 0.0,
    required this.createdBy,
    required this.createdAt,
  });

  factory GlobalEvent.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return GlobalEvent(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      eventType: data['eventType'] ?? '',
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      settings: Map<String, dynamic>.from(data['settings'] ?? {}),
      assignedAdmins: List<String>.from(data['assignedAdmins'] ?? []),
      status: data['status'] ?? 'planned',
      sponsors: Map<String, dynamic>.from(data['sponsors'] ?? {}),
      prizePool: (data['prizePool'] ?? 0.0).toDouble(),
      createdBy: data['createdBy'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'eventType': eventType,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'settings': settings,
      'assignedAdmins': assignedAdmins,
      'status': status,
      'sponsors': sponsors,
      'prizePool': prizePool,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

// Audit Log Model
class AuditLog {
  final String id;
  final String adminId;
  final String action;
  final String targetType; // user, content, settings, etc.
  final String targetId;
  final Map<String, dynamic> details;
  final DateTime timestamp;
  final String ipAddress;
  final String userAgent;
  final String? reason;
  final Map<String, dynamic> beforeState;
  final Map<String, dynamic> afterState;

  AuditLog({
    required this.id,
    required this.adminId,
    required this.action,
    required this.targetType,
    required this.targetId,
    this.details = const {},
    required this.timestamp,
    required this.ipAddress,
    required this.userAgent,
    this.reason,
    this.beforeState = const {},
    this.afterState = const {},
  });

  factory AuditLog.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AuditLog(
      id: doc.id,
      adminId: data['adminId'] ?? '',
      action: data['action'] ?? '',
      targetType: data['targetType'] ?? '',
      targetId: data['targetId'] ?? '',
      details: Map<String, dynamic>.from(data['details'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      ipAddress: data['ipAddress'] ?? '',
      userAgent: data['userAgent'] ?? '',
      reason: data['reason'],
      beforeState: Map<String, dynamic>.from(data['beforeState'] ?? {}),
      afterState: Map<String, dynamic>.from(data['afterState'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'adminId': adminId,
      'action': action,
      'targetType': targetType,
      'targetId': targetId,
      'details': details,
      'timestamp': Timestamp.fromDate(timestamp),
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'reason': reason,
      'beforeState': beforeState,
      'afterState': afterState,
    };
  }
}
