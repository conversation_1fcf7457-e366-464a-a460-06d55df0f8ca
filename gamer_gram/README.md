# GamerGram 🎮

A gamer-focused social media app like Instagram, built with Flutter and Firebase.

## Features

### 🔐 Authentication
- Email and password registration/login
- Google Sign-In integration
- Discord login (planned)
- Password reset functionality
- Secure user profile creation

### 👤 User Profiles
- Custom usernames and gamertags
- Profile pictures and avatars
- Bio and favorite games
- Follower/following system
- Gaming statistics and achievements

### 📱 Feed & Content
- Image and video posts
- Gaming screenshots and memes
- Gameplay moments and highlights
- Custom reactions (🔥, GG, 💀, 🏆)
- Like, comment, and share functionality

### 🎬 Reels & Stories
- Short video clips for gameplay highlights
- Music and filter integration
- Stories that disappear in 24 hours
- Vertical scrolling interface

### 💬 Messaging
- Direct messaging with text and emojis
- GIF support via Giphy
- Voice notes and audio messages
- Real-time chat functionality

### 📞 Voice/Video Calling
- One-to-one voice calls
- Group video calls
- Screen sharing for gameplay
- Call quality optimization

### 🔔 Notifications
- Push notifications for likes, comments, follows
- Squad invites and gaming events
- Real-time updates
- Customizable notification settings

### 🔍 Explore
- Discover trending clips and top gamers
- Gaming communities and tournaments
- Hashtag-based content discovery
- Personalized recommendations

## Tech Stack

### Frontend
- **Flutter** - Cross-platform mobile development
- **Riverpod** - State management
- **Go Router** - Navigation
- **Google Fonts** - Typography
- **Lottie** - Animations

### Backend & Services
- **Firebase Auth** - Authentication
- **Cloud Firestore** - Database
- **Firebase Storage** - File storage
- **Firebase Cloud Messaging** - Push notifications
- **Firebase Analytics** - User analytics

### Media & Features
- **Video Player** - Video playback
- **Camera** - Photo/video capture
- **Image Picker** - Media selection
- **Record** - Audio recording
- **WebSocket** - Real-time features

## Project Structure

```
lib/
├── core/
│   ├── constants/
│   │   ├── app_colors.dart
│   │   └── app_theme.dart
│   ├── errors/
│   ├── network/
│   └── utils/
├── features/
│   ├── auth/
│   │   ├── data/
│   │   ├── domain/
│   │   │   └── models/
│   │   └── presentation/
│   │       ├── providers/
│   │       └── screens/
│   ├── feed/
│   ├── profile/
│   ├── reels/
│   ├── messages/
│   ├── stories/
│   └── explore/
└── shared/
    ├── widgets/
    ├── models/
    └── services/
```

## Getting Started

### Prerequisites
- Flutter SDK (3.0.0 or higher)
- Dart SDK
- Android Studio / VS Code
- Firebase project setup

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/gamer-gram.git
   cd gamer-gram
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a new Firebase project
   - Enable Authentication (Email/Password, Google)
   - Create Firestore database
   - Enable Storage
   - Add your `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)

4. **Run the app**
   ```bash
   flutter run
   ```

### Firebase Configuration

1. **Authentication**
   - Enable Email/Password authentication
   - Configure Google Sign-In
   - Set up password reset

2. **Firestore Rules**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /users/{userId} {
         allow read: if request.auth != null;
         allow write: if request.auth != null && request.auth.uid == userId;
       }
       match /posts/{postId} {
         allow read: if request.auth != null;
         allow write: if request.auth != null;
       }
     }
   }
   ```

3. **Storage Rules**
   ```javascript
   rules_version = '2';
   service firebase.storage {
     match /b/{bucket}/o {
       match /{allPaths=**} {
         allow read, write: if request.auth != null;
       }
     }
   }
   ```

## Architecture

The app follows Clean Architecture principles with MVVM pattern:

- **Presentation Layer**: UI components, screens, and state management
- **Domain Layer**: Business logic, entities, and use cases
- **Data Layer**: Repositories, data sources, and external APIs

### State Management
- **Riverpod** for dependency injection and state management
- **StreamProvider** for real-time data
- **StateNotifierProvider** for complex state logic

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Roadmap

### Phase 1 (Current)
- ✅ Basic authentication
- ✅ User profiles
- ✅ Feed implementation
- ✅ Bottom navigation

### Phase 2
- [ ] Reels and stories
- [ ] Direct messaging
- [ ] Push notifications
- [ ] Media upload

### Phase 3
- [ ] Voice/video calling
- [ ] Gaming communities
- [ ] Tournament features
- [ ] Advanced analytics

### Phase 4
- [ ] Discord integration
- [ ] Cross-platform sync
- [ ] Advanced filters
- [ ] AI-powered recommendations

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or join our Discord server.

---

**GamerGram** - Connect. Share. Game. 🎮
