name: gamer_gram
description: A gamer-focused social media app like Instagram
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # UI & Design
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  
  # Navigation
  go_router: ^12.1.3
  
  # Authentication
  google_sign_in: ^6.1.6
  sign_in_with_apple: ^5.0.0
  
  # Media & Video
  video_player: ^2.8.1
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  photo_manager: ^2.8.1
  video_compress: ^3.1.2
  
  # Audio & Voice
  record: ^5.0.4
  audioplayers: ^5.2.1
  permission_handler: ^11.1.0
  
  # Networking & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Utils
  intl: ^0.18.1
  uuid: ^4.2.1
  url_launcher: ^6.2.2
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.1
  
  # Notifications
  flutter_local_notifications: ^16.3.0
  
  # Real-time features
  web_socket_channel: ^2.4.0
  
  # GIF & Emoji
  giphy_picker: ^1.0.0
  emoji_picker_flutter: ^1.6.3
  
  # QR Code
  qr_flutter: ^4.1.0
  
  # Charts & Analytics
  fl_chart: ^0.65.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  riverpod_generator: ^2.3.9
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^7.0.8
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: Gaming
      fonts:
        - asset: assets/fonts/Gaming-Regular.ttf
        - asset: assets/fonts/Gaming-Bold.ttf
          weight: 700
