import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String email;
  final String username;
  final String? displayName;
  final String? photoURL;
  final String? gamertag;
  final String? bio;
  final List<String> favoriteGames;
  final List<String> followers;
  final List<String> following;
  final int postCount;
  final int followerCount;
  final int followingCount;
  final bool isVerified;
  final bool isPrivate;
  final DateTime createdAt;
  final DateTime lastSeen;
  final Map<String, dynamic>? settings;
  final String? fcmToken;

  UserModel({
    required this.id,
    required this.email,
    required this.username,
    this.displayName,
    this.photoURL,
    this.gamertag,
    this.bio,
    this.favoriteGames = const [],
    this.followers = const [],
    this.following = const [],
    this.postCount = 0,
    this.followerCount = 0,
    this.followingCount = 0,
    this.isVerified = false,
    this.isPrivate = false,
    required this.createdAt,
    required this.lastSeen,
    this.settings,
    this.fcmToken,
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      email: data['email'] ?? '',
      username: data['username'] ?? '',
      displayName: data['displayName'],
      photoURL: data['photoURL'],
      gamertag: data['gamertag'],
      bio: data['bio'],
      favoriteGames: List<String>.from(data['favoriteGames'] ?? []),
      followers: List<String>.from(data['followers'] ?? []),
      following: List<String>.from(data['following'] ?? []),
      postCount: data['postCount'] ?? 0,
      followerCount: data['followerCount'] ?? 0,
      followingCount: data['followingCount'] ?? 0,
      isVerified: data['isVerified'] ?? false,
      isPrivate: data['isPrivate'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastSeen: (data['lastSeen'] as Timestamp).toDate(),
      settings: data['settings'],
      fcmToken: data['fcmToken'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'username': username,
      'displayName': displayName,
      'photoURL': photoURL,
      'gamertag': gamertag,
      'bio': bio,
      'favoriteGames': favoriteGames,
      'followers': followers,
      'following': following,
      'postCount': postCount,
      'followerCount': followerCount,
      'followingCount': followingCount,
      'isVerified': isVerified,
      'isPrivate': isPrivate,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastSeen': Timestamp.fromDate(lastSeen),
      'settings': settings,
      'fcmToken': fcmToken,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? username,
    String? displayName,
    String? photoURL,
    String? gamertag,
    String? bio,
    List<String>? favoriteGames,
    List<String>? followers,
    List<String>? following,
    int? postCount,
    int? followerCount,
    int? followingCount,
    bool? isVerified,
    bool? isPrivate,
    DateTime? createdAt,
    DateTime? lastSeen,
    Map<String, dynamic>? settings,
    String? fcmToken,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      gamertag: gamertag ?? this.gamertag,
      bio: bio ?? this.bio,
      favoriteGames: favoriteGames ?? this.favoriteGames,
      followers: followers ?? this.followers,
      following: following ?? this.following,
      postCount: postCount ?? this.postCount,
      followerCount: followerCount ?? this.followerCount,
      followingCount: followingCount ?? this.followingCount,
      isVerified: isVerified ?? this.isVerified,
      isPrivate: isPrivate ?? this.isPrivate,
      createdAt: createdAt ?? this.createdAt,
      lastSeen: lastSeen ?? this.lastSeen,
      settings: settings ?? this.settings,
      fcmToken: fcmToken ?? this.fcmToken,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, username: $username, gamertag: $gamertag)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
