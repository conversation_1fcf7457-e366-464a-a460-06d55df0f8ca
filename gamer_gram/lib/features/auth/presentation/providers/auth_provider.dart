import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gamer_gram/features/auth/domain/models/user_model.dart';
import 'package:gamer_gram/shared/services/firebase_service.dart';

// Auth state provider
final authStateProvider = StreamProvider<User?>((ref) {
  return FirebaseService.auth.authStateChanges();
});

// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  return FirebaseService.currentUser;
});

// User model provider
final userModelProvider = StreamProvider.family<UserModel?, String>((ref, userId) {
  return FirebaseService.firestore
      .collection('users')
      .doc(userId)
      .snapshots()
      .map((doc) {
    if (doc.exists) {
      return UserModel.fromFirestore(doc);
    }
    return null;
  });
});

// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref);
});

// Auth state
class AuthState {
  final bool isLoading;
  final String? error;
  final UserModel? user;

  AuthState({
    this.isLoading = false,
    this.error,
    this.user,
  });

  AuthState copyWith({
    bool? isLoading,
    String? error,
    UserModel? user,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      user: user ?? this.user,
    );
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final Ref _ref;

  AuthNotifier(this._ref) : super(AuthState());

  // Sign in with email and password
  Future<void> signInWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final userCredential = await FirebaseService.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (userCredential.user != null) {
        await _updateUserLastSeen(userCredential.user!.uid);
      }
      
      state = state.copyWith(isLoading: false);
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getAuthErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
    }
  }

  // Sign up with email and password
  Future<void> signUpWithEmailAndPassword(
    String email,
    String password,
    String username,
    String? gamertag,
  ) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      // Check if username is available
      final usernameExists = await _checkUsernameExists(username);
      if (usernameExists) {
        state = state.copyWith(
          isLoading: false,
          error: 'Username already exists',
        );
        return;
      }
      
      final userCredential = await FirebaseService.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (userCredential.user != null) {
        await _createUserProfile(
          userCredential.user!,
          username,
          gamertag,
        );
      }
      
      state = state.copyWith(isLoading: false);
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getAuthErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
    }
  }

  // Sign in with Google
  Future<void> signInWithGoogle() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
      if (googleUser == null) {
        state = state.copyWith(isLoading: false);
        return;
      }
      
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      final userCredential = await FirebaseService.auth.signInWithCredential(credential);
      
      if (userCredential.user != null) {
        // Check if user profile exists
        final userDoc = await FirebaseService.firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .get();
        
        if (!userDoc.exists) {
          // Create user profile for new Google user
          await _createUserProfile(
            userCredential.user!,
            userCredential.user!.displayName?.toLowerCase().replaceAll(' ', '') ?? 'user${userCredential.user!.uid.substring(0, 6)}',
            null,
          );
        } else {
          await _updateUserLastSeen(userCredential.user!.uid);
        }
      }
      
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to sign in with Google',
      );
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      state = state.copyWith(isLoading: true);
      await FirebaseService.signOut();
      await GoogleSignIn().signOut();
      state = state.copyWith(isLoading: false, user: null);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to sign out',
      );
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await FirebaseService.auth.sendPasswordResetEmail(email: email);
      state = state.copyWith(isLoading: false);
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getAuthErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
    }
  }

  // Check if username exists
  Future<bool> _checkUsernameExists(String username) async {
    final query = await FirebaseService.firestore
        .collection('users')
        .where('username', isEqualTo: username)
        .get();
    return query.docs.isNotEmpty;
  }

  // Create user profile
  Future<void> _createUserProfile(User user, String username, String? gamertag) async {
    final fcmToken = await FirebaseService.getFCMToken();
    
    final userModel = UserModel(
      id: user.uid,
      email: user.email ?? '',
      username: username,
      displayName: user.displayName,
      photoURL: user.photoURL,
      gamertag: gamertag,
      createdAt: DateTime.now(),
      lastSeen: DateTime.now(),
      fcmToken: fcmToken,
    );
    
    await FirebaseService.firestore
        .collection('users')
        .doc(user.uid)
        .set(userModel.toFirestore());
  }

  // Update user last seen
  Future<void> _updateUserLastSeen(String userId) async {
    await FirebaseService.firestore
        .collection('users')
        .doc(userId)
        .update({
      'lastSeen': Timestamp.fromDate(DateTime.now()),
    });
  }

  // Get auth error message
  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email';
      case 'wrong-password':
        return 'Wrong password';
      case 'email-already-in-use':
        return 'Email is already registered';
      case 'weak-password':
        return 'Password is too weak';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'User account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Try again later';
      default:
        return 'Authentication failed';
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}
