import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:gamer_gram/core/constants/app_colors.dart';
import 'package:gamer_gram/core/constants/app_theme.dart';
import 'package:gamer_gram/features/auth/presentation/providers/auth_provider.dart';
import 'package:gamer_gram/features/auth/presentation/screens/splash_screen.dart';
import 'package:gamer_gram/shared/services/firebase_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Initialize Firebase services
  await FirebaseService.initialize();
  
  runApp(
    const ProviderScope(
      child: GamerGramApp(),
    ),
  );
}

class GamerGramApp extends ConsumerWidget {
  const GamerGramApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'GamerGram',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const SplashScreen(),
    );
  }
}
